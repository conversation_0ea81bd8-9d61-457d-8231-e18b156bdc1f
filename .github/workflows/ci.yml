name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: "20"

jobs:
  lint-and-typecheck:
    name: <PERSON><PERSON> and <PERSON> Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Type check
        run: npm run typecheck

  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: ["18", "20", "22"]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests
        run: npm run test:unit:ci

      - name: Upload unit test coverage to Codecov
        if: matrix.node-version == '20'
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage/unit/lcov.info
          flags: unittests
          name: codecov-unit-tests
          fail_ci_if_error: false

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [unit-tests] # Run after unit tests pass
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Install Tailscale CLI
        run: |
          curl -fsSL https://tailscale.com/install.sh | sh
          sudo tailscale version

      - name: Setup Tailscale for testing
        env:
          TAILSCALE_AUTH_KEY: ${{ secrets.TAILSCALE_AUTH_KEY }}
        run: |
          if [ -n "$TAILSCALE_AUTH_KEY" ]; then
            echo "🔐 Authenticating Tailscale with auth key..."
            sudo tailscale up --authkey="$TAILSCALE_AUTH_KEY" --hostname="ci-runner-${{ github.run_id }}" --accept-routes
            echo "✅ Tailscale authenticated successfully"
            sudo tailscale status
          else
            echo "⚠️  No Tailscale auth key provided. Integration tests will run in CLI-only mode."
            echo "   Some tests may be skipped. To enable full integration testing,"
            echo "   add TAILSCALE_AUTH_KEY to repository secrets."
          fi

      - name: Run integration tests
        run: npm run test:integration:ci
        env:
          # Allow tests to run even without full Tailscale setup
          TAILSCALE_TEST_MODE: "ci"

      - name: Upload integration test coverage
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage/integration/lcov.info
          flags: integration
          name: codecov-integration-tests
          fail_ci_if_error: false

      - name: Cleanup Tailscale
        if: always()
        run: |
          if command -v tailscale >/dev/null 2>&1; then
            echo "🧹 Cleaning up Tailscale..."
            sudo tailscale logout || true
            sudo tailscale down || true
          fi

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: [lint-and-typecheck, unit-tests, integration-tests]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Verify build artifacts
        run: |
          test -f dist/index.js
          test -f dist/index.cjs
          echo "✅ Build artifacts verified"

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: dist/
          retention-days: 7

  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: npm audit --audit-level=moderate

      - name: Run dependency check
        run: npx audit-ci --moderate
