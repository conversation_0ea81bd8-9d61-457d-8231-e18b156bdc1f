name: Release

on:
  push:
    branches: [main]
  workflow_dispatch:
    inputs:
      release_type:
        description: "Release type"
        required: true
        default: "patch"
        type: choice
        options:
          - patch
          - minor
          - major
          - prerelease

env:
  NODE_VERSION: "20"
  # Define the artifact name for the build output
  ARTIFACT_NAME: tailscale-mcp-server

# Grant minimum necessary permissions for the GITHUB_TOKEN
permissions:
  contents: write
  packages: write
  id-token: write

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    if: ${{ !contains(github.event.head_commit.message, '[skip ci]') }}
    outputs:
      # Correctly map outputs to the step that generates them
      released: ${{ steps.version.outputs.released }}
      version: ${{ steps.version.outputs.new_version }}
      tag: ${{ steps.version.outputs.tag }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm run test:ci

      - name: Build project
        run: npm run build

      - name: Verify build
        run: |
          test -f dist/index.js
          test -f dist/index.cjs
          echo "✅ Build verification passed"

      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.ARTIFACT_NAME }}
          path: ./dist

      - name: Configure Git
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

      - name: Determine version bump
        id: version_bump
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "type=${{ github.event.inputs.release_type }}" >> $GITHUB_OUTPUT
          else
            if git log --format=%B -n 1 ${{ github.sha }} | grep -q "BREAKING CHANGE\|!:"; then
              echo "type=major" >> $GITHUB_OUTPUT
            elif git log --format=%B -n 1 ${{ github.sha }} | grep -q "^feat"; then
              echo "type=minor" >> $GITHUB_OUTPUT
            else
              echo "type=patch" >> $GITHUB_OUTPUT
            fi
          fi

      - name: Bump version and generate changelog
        id: version
        run: |
          OLD_VERSION=$(node -p "require('./package.json').version")
          npm version ${{ steps.version_bump.outputs.type }} --no-git-tag-version
          NEW_VERSION=$(node -p "require('./package.json').version")

          echo "old_version=$OLD_VERSION" >> $GITHUB_OUTPUT
          echo "new_version=$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "tag=v$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "released=true" >> $GITHUB_OUTPUT # Set output directly

          # Generate changelog
          CHANGELOG_BODY=$(git log --pretty=format:"- %s (%h)" v$OLD_VERSION..HEAD || git log
