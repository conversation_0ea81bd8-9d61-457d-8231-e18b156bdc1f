{"name": "@hexsleeves/tailscale-mcp-server", "version": "0.2.3", "description": "A Model Context Protocol (MCP) server that provides seamless integration with Tailscale's CLI commands and REST API, enabling automated network management and monitoring through a standardized interface.", "keywords": ["tailscale", "mcp", "model context protocol", "tailscale mcp", "tailscale mcp server"], "author": "HexSleeves", "license": "MIT", "type": "module", "main": "dist/index.js", "bin": {"tailscale-mcp-server": "./dist/index.js"}, "files": ["dist/**/*", "README.md", "LICENSE"], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "node esbuild.config.js build", "build:dev": "node esbuild.config.js dev", "build:watch": "node esbuild.config.js watch", "start": "node dist/index.js", "dev": "npm run build:dev && node dist/index.js", "dev:watch": "npm run build:watch", "dev:direct": "tsx src/index.ts", "typecheck": "tsc --noEmit", "lint": "eslint .", "format": "eslint --fix . && prettier --write .", "test": "jest", "test:unit": "jest --config jest.config.unit.ts", "test:integration": "jest --config jest.config.integration.ts", "test:watch": "jest --watch", "test:unit:watch": "jest --config jest.config.unit.ts --watch", "test:integration:watch": "jest --config jest.config.integration.ts --watch", "test:coverage": "jest --coverage", "test:unit:coverage": "jest --config jest.config.unit.ts --coverage", "test:integration:coverage": "jest --config jest.config.integration.ts --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:unit:ci": "jest --config jest.config.unit.ts --ci --coverage --watchAll=false", "test:integration:ci": "jest --config jest.config.integration.ts --ci --coverage --watchAll=false", "test:setup": "./scripts/setup-testing.sh", "qa": "npm run typecheck && npm run test:unit && npm run lint", "qa:full": "npm run typecheck && npm run test && npm run lint", "inspector": "npx @modelcontextprotocol/inspector node dist/index.js", "prepublishOnly": "npm run qa:full && npm run build", "publish": "./scripts/publish.sh", "publish:test": "./scripts/test-publish.sh"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "axios": "^1.9.0", "dotenv": "^16.5.0", "express": "^5.1.0", "zod": "^3.25.56"}, "devDependencies": {"@eslint/js": "^9.28.0", "@eslint/json": "^0.12.0", "@eslint/markdown": "^6.5.0", "@jest/globals": "^29.7.0", "@types/express": "^5.0.3", "@types/jest": "^29.5.8", "@types/node": "^22.15.30", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "audit-ci": "^7.1.0", "esbuild": "^0.25.5", "eslint": "^9.28.0", "globals": "^16.2.0", "jest": "^29.7.0", "prettier": "3.5.3", "rimraf": "^6.0.1", "semver": "^7.7.2", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1"}}