# Tailscale MCP Server Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# TAILSCALE API CONFIGURATION
# =============================================================================

# Required for API operations (get your API key from https://login.tailscale.com/admin/settings/keys)
TAILSCALE_API_KEY=tskey-api-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Your tailnet name (usually your organization name or email domain)
# You can find this in the Tailscale admin console URL: https://login.tailscale.com/admin/machines/{tailnet}
TAILSCALE_TAILNET=your-tailnet-name

# Optional: Custom API base URL (defaults to https://api.tailscale.com)
# TAILSCALE_API_BASE_URL=https://api.tailscale.com

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level: 0=DEBUG, 1=INFO, 2=WARN, 3=ERROR (defaults to 1)
LOG_LEVEL=1

# Optional: Enable server file logging (supports {timestamp} placeholder)
# MCP_SERVER_LOG_FILE=logs/tailscale-mcp-server-{timestamp}.log

# =============================================================================
# TEST SCRIPT CONFIGURATION
# =============================================================================

# Optional: Server startup timeout in milliseconds (defaults to 10000)
# MCP_START_TIMEOUT=15000

# Optional: Enable test script file logging (supports {timestamp} placeholder)
# MCP_LOG_FILE=logs/test-session-{timestamp}.log

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Optional: Enable debug mode for development
# NODE_ENV=development

# Optional: Enable verbose CLI output
# TAILSCALE_DEBUG=1
