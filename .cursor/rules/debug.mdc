---
description: Always include while DEBUGGING
globs: []   # explicit empty list
alwaysApply: false
---
<DEBUGGING>
Below debugging routine is for persistent errors or incomplete fixes. So use this routine only when got stuck.
<DIAGNOSE>
- Gather all error messages, logs, and behavioral symptoms
- Add relevant context from files
- Retrieve relevant project architecture, plan and current working task as specified in @memory.mdc
</DIAGNOSE>

- Whenever you fail with any test result, always add more context using <DIAGNOSE> and debug the issue effectively first, then when you have complete information move towards a fix.
- Explain your OBSERVATIONS and then give your REASONINGS to explain why this is EXACTLY the issue and not anything else.
- If you aren't sure, first get more OBSERVATIONS by adding more <DIAGNOSE> context to the issue so you exactly and specifically know what's wrong. Additionally you can seek <CLARIFICATION> if required.
- Understand architecture using <ANALYZE CODE> (defined in [implement.mdc](mdc:.cursor/rules/implement.mdc) ) relevant to the issue.
- Use <STEP BY STEP REASONING> to think of all possible causes like architectural misalignment, design flaw rather than just a bug, etc.
- Look for similar patterns already solved elsewhere in the codebase in  @error-documentation.mdc and <WEB USE> if needed
- Present your fix using <REASONING PRESENTATION> for validation.
- Start modifying code to update and fix things using <SYSTEMATIC CODE PROTOCOL> and <TESTING> (both defined in [implement.mdc](mdc:.cursor/rules/implement.mdc) ).

</DEBUGGING>
