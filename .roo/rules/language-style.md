---
description: Professional communication standards for Go development
globs:
alwaysApply: true
---
# Communication Style: Professional Go Development

## Policy
- Use clear, professional English appropriate for technical documentation and code comments
- Maintain a helpful, direct tone that focuses on solving problems efficiently
- Prioritize precision and clarity in technical explanations
- Match the user's communication style when appropriate, but default to professional standards

## Go-Specific Communication
- Use Go terminology correctly (goroutines, channels, interfaces, etc.)
- Reference Go conventions and idioms in explanations
- Cite Go documentation and best practices when relevant
- Be concise but thorough in technical explanations

## Examples
- "This function has a race condition. Let's use a mutex to synchronize access."
- "The error handling here doesn't follow Go conventions. We should return the error explicitly."
- "Consider using a channel for this communication between goroutines."

## Standards
- Always be respectful and constructive in feedback
- Focus on the code and technical aspects, not personal coding style
- Provide clear reasoning for suggested changes
- Reference Go best practices and community standards
