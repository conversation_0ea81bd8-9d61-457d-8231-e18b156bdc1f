# MCP API Specification

This document describes the Model Context Protocol (MCP) API implementation for the Tailscale MCP Server.

## Protocol Version

- **MCP Version**: 2024-11-05
- **Server Implementation**: Tailscale MCP Server v1.0.0+

## Server Information

```json
{
  "name": "tailscale-mcp-server",
  "version": "1.0.0"
}
```

## Available Tools

### 1. Device Management Tool

**Name**: `device_management`

**Description**: Manage Tailscale devices including listing, status, and configuration

**Input Schema**:

```json
{
  "type": "object",
  "properties": {
    "action": {
      "type": "string",
      "description": "Action to perform",
      "enum": ["list", "status", "enable", "disable"]
    },
    "device_id": {
      "type": "string",
      "description": "Device ID for specific operations"
    }
  },
  "required": ["action"]
}
```

**Example Usage**:

```json
{
  "name": "device_management",
  "arguments": {
    "action": "list"
  }
}
```

### 2. Network Tool

**Name**: `network`

**Description**: Network operations including ping, connectivity tests, and route management

**Input Schema**:

```json
{
  "type": "object",
  "properties": {
    "action": {
      "type": "string",
      "description": "Network action to perform",
      "enum": ["ping", "routes", "connectivity", "ip"]
    },
    "target": {
      "type": "string",
      "description": "Target host or IP for network operations"
    },
    "count": {
      "type": "integer",
      "description": "Number of ping packets to send",
      "default": 4
    }
  },
  "required": ["action"]
}
```

**Example Usage**:

```json
{
  "name": "network",
  "arguments": {
    "action": "ping",
    "target": "**********",
    "count": 3
  }
}
```

### 3. Admin Tool

**Name**: `admin`

**Description**: Administrative operations including user management, settings, and system configuration

**Input Schema**:

```json
{
  "type": "object",
  "properties": {
    "action": {
      "type": "string",
      "description": "Administrative action to perform",
      "enum": ["status", "logout", "login", "up", "down", "version"]
    },
    "auth_key": {
      "type": "string",
      "description": "Authentication key for login operations"
    },
    "hostname": {
      "type": "string",
      "description": "Hostname for the device"
    }
  },
  "required": ["action"]
}
```

**Example Usage**:

```json
{
  "name": "admin",
  "arguments": {
    "action": "status"
  }
}
```

### 4. ACL Tool

**Name**: `acl`

**Description**: Access Control List management including viewing, updating, and validating ACL policies

**Input Schema**:

```json
{
  "type": "object",
  "properties": {
    "action": {
      "type": "string",
      "description": "ACL action to perform",
      "enum": ["get", "set", "validate", "test"]
    },
    "policy": {
      "type": "string",
      "description": "ACL policy JSON for set operations"
    },
    "source": {
      "type": "string",
      "description": "Source IP or user for ACL testing"
    },
    "destination": {
      "type": "string",
      "description": "Destination IP or service for ACL testing"
    },
    "port": {
      "type": "integer",
      "description": "Port number for ACL testing"
    }
  },
  "required": ["action"]
}
```

**Example Usage**:

```json
{
  "name": "acl",
  "arguments": {
    "action": "get"
  }
}
```

## Error Handling

The server follows MCP error code conventions:

- `-32700`: Parse error
- `-32600`: Invalid request
- `-32601`: Method not found
- `-32602`: Invalid params
- `-32603`: Internal error
- `-32000`: Unsupported protocol
- `-32001`: Tool not found
- `-32002`: Tool execution error

## Authentication

The server requires the following environment variables for API operations:

- `TAILSCALE_API_KEY`: Tailscale API key
- `TAILSCALE_TAILNET`: Tailnet name

## Rate Limiting

The server implements rate limiting to prevent abuse:

- **CLI Operations**: 10 requests per minute
- **API Operations**: 100 requests per minute

## Logging

All operations are logged with structured logging. Log levels:

- `0`: Debug
- `1`: Info (default)
- `2`: Warning
- `3`: Error

## Health Checks

The server provides health check endpoints when running in HTTP mode:

- `GET /health`: Basic health check
- `GET /ready`: Readiness check

## Metrics

When enabled, the server exposes metrics in Prometheus format:

- `GET /metrics`: Prometheus metrics endpoint
