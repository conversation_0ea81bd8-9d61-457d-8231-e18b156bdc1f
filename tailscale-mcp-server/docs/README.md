# Documentation

This directory contains comprehensive documentation for the Tailscale MCP Server.

## Table of Contents

- [Installation Guide](installation.md)
- [Configuration](configuration.md)
- [API Reference](api-reference.md)
- [Tool Documentation](tools.md)
- [Development Guide](development.md)
- [Deployment Guide](deployment.md)
- [Troubleshooting](troubleshooting.md)
- [Examples](../examples/)

## Quick Start

1. **Installation**: See [installation.md](installation.md) for detailed setup instructions
2. **Configuration**: Configure your environment variables as described in [configuration.md](configuration.md)
3. **Usage**: Start the server with `tailscale-mcp-server serve`

## Architecture Overview

The Tailscale MCP Server is built with a modular architecture:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MCP Client    │◄──►│  MCP Server     │◄──►│  Tailscale      │
│   (Claude, etc) │    │  (This Project) │    │  CLI/API        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Core Components

- **MCP Server**: Implements the Model Context Protocol specification
- **Tool Registry**: Manages available tools and their execution
- **Tailscale Integration**: Provides CLI and API client implementations
- **Configuration System**: Handles environment-based configuration
- **Logging System**: Structured logging with configurable levels

### Supported Tools

- **Device Management**: List, enable, disable devices
- **Network Operations**: Ping, connectivity tests, IP information
- **Administrative Functions**: Login, logout, status, version
- **ACL Management**: View, update, validate access control lists

## Contributing

See [CONTRIBUTING.md](../CONTRIBUTING.md) for development guidelines and contribution instructions.

## Support

- **Issues**: Report bugs and feature requests on GitHub
- **Discussions**: Join community discussions for questions and ideas
- **Documentation**: Check this documentation for common questions

## License

This project is licensed under the MIT License. See [LICENSE](../LICENSE) for details.
